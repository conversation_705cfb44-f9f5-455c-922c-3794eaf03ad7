from datetime import (
    datetime,
    timedelta,
)
from decimal import Decimal
from unittest.mock import patch

from django.core.exceptions import ValidationError

import pytest

from custom.enums import ShelfType
from gallery.constants import FURNITURE_DYNAMIC_COLOR_CHOICES
from gallery.enums import FurnitureCategory
from regions.models import Region
from vouchers.enums import VoucherType

NOW = datetime(2025, 6, 11, 10, 0, 0)


@pytest.mark.django_db
class TestVoucher:
    def test_calculate_price_with_percentage_voucher(self, voucher_factory):
        voucher = voucher_factory(value=25, kind_of=1)

        assert voucher.calculate_price(1000) == 750

    def test_calculate_price_with_absolute_voucher(
        self,
        voucher_factory,
        rates_neutral_region,
    ):
        voucher = voucher_factory(value=25, kind_of=0)

        assert voucher.calculate_price(1000, rates_neutral_region) == 975

    def test_item_filters_for_voucher(self, voucher_factory):
        voucher = voucher_factory(
            item_conditionals={'shelf_types': [1, 2], 'materials': [0]}
        )
        assert 'shelf_type__in' in voucher.item_filters
        assert 'material__in' in voucher.item_filters

    def test_voucher_create_region_entries(self, voucher_factory, region_factory):
        # delete auto-created regions
        Region.objects.all().delete()

        voucher = voucher_factory(is_absolute=True)
        region_factory.create_batch(15)
        assert not voucher.region_entries.all()

        voucher.create_region_entries()
        voucher.refresh_from_db()
        # 15 regions from region_factory and one from voucher factory (user profile)
        assert len(voucher.region_entries.all()) == 16

    def test_calculate_price_with_discounts_with_deleted_order_item(
        self,
        voucher,
        order_factory,
        order_item_factory,
    ):
        order = order_factory(
            items=[],
            assembly=False,
        )
        order_item_factory(
            order=order,
            order_item__deleted=True,
        )
        order_item = order_item_factory(order=order)

        price_with_discount = voucher.calculate_price_with_discounts(
            order, promotion_affected_items=[]
        )

        assert price_with_discount == order_item.region_price.quantize(Decimal('1'))

    def test_subtract_quantity_left_when_quantity_is_less_than_equal_0(
        self,
        voucher_factory,
    ):
        voucher = voucher_factory(quantity_left=0)
        voucher.subtract_quantity_left()

        assert voucher.quantity_left == 0

    def test_subtract_quantity_left_when_quantity_is_above_0(
        self,
        voucher_factory,
    ):
        voucher = voucher_factory(quantity_left=3)
        voucher.subtract_quantity_left()

        assert voucher.quantity_left == 2


@pytest.mark.django_db
class TestVoucherWithItemDiscount:
    @patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=1000)
    @patch('gallery.models.Jetty.get_delivery_price', return_value=0)
    @pytest.mark.parametrize(
        'field_name, value',  # noqa: PT006
        [
            ('shelf_type', ShelfType.TYPE01.value),
            ('shelf_type', ShelfType.TYPE02.value),
            ('shelf_type', ShelfType.VENEER_TYPE01.value),
            ('material', FURNITURE_DYNAMIC_COLOR_CHOICES[0][0]),
            ('material', FURNITURE_DYNAMIC_COLOR_CHOICES[1][0]),
            ('material', FURNITURE_DYNAMIC_COLOR_CHOICES[2][0]),
            ('material', FURNITURE_DYNAMIC_COLOR_CHOICES[3][0]),
        ],
    )
    def test_jetty_item_is_discounted(
        self,
        mocked_delivery_price,
        mocked_shelf_price_as_number,
        field_name,
        value,
        order_item_factory,
        item_discount_factory,
        jetty_factory,
        percentage_voucher,
        empty_order,
        rates_neutral_region,
    ):
        region_price = 1000
        jetty = jetty_factory(**{field_name: value})
        order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty,
            region_price=region_price,
        )
        empty_order.region_total_price = region_price
        empty_order.save(update_fields=['region_total_price'])
        percentage_voucher.discounts.add(
            item_discount_factory(
                **{
                    'value': 20,
                    field_name: value,
                }
            )
        )
        percentage_voucher.save()
        price = percentage_voucher.calculate_promo_amount(
            instance=empty_order,
            region=rates_neutral_region,
        )
        assert price.quantize(Decimal('.01')) == 200.00

    @patch('gallery.models.Watty.get_shelf_price_as_number', return_value=1000)
    @patch('gallery.models.Watty.get_delivery_price', return_value=0)
    @pytest.mark.parametrize(
        'field_name, value',  # noqa: PT006
        [
            ('shelf_type', ShelfType.TYPE03.value),
            ('shelf_type', ShelfType.TYPE13.value),
            ('material', FURNITURE_DYNAMIC_COLOR_CHOICES[0][0]),
            ('material', FURNITURE_DYNAMIC_COLOR_CHOICES[1][0]),
            ('material', FURNITURE_DYNAMIC_COLOR_CHOICES[2][0]),
            ('material', FURNITURE_DYNAMIC_COLOR_CHOICES[3][0]),
        ],
    )
    def test_watty_item_is_discounted(
        self,
        mocked_delivery_price,
        mocked_shelf_price_as_number,
        field_name,
        value,
        order_item_factory,
        item_discount_factory,
        watty_factory,
        percentage_voucher,
        empty_order,
        rates_neutral_region,
    ):
        region_price = 1000
        watty = watty_factory(**{field_name: value})
        order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=watty,
            region_price=region_price,
        )
        empty_order.region_total_price = region_price
        empty_order.save(update_fields=['region_total_price'])
        percentage_voucher.discounts.add(
            item_discount_factory(
                **{
                    'value': 20,
                    field_name: value,
                }
            )
        )
        percentage_voucher.save()
        price = percentage_voucher.calculate_promo_amount(
            instance=empty_order,
            region=rates_neutral_region,
        )
        assert price.quantize(Decimal('.01')) == 200.0

    @patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=1000)
    @patch('gallery.models.Jetty.get_delivery_price', return_value=0)
    def test_item_not_in_conditions_has_base_discount_applied(
        self,
        mocked_delivery_price,
        mocked_shelf_price_as_number,
        order_item_factory,
        item_discount_factory,
        jetty_factory,
        percentage_voucher,
        empty_order,
        rates_neutral_region,
    ):
        region_price = 1000
        order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(shelf_type=ShelfType.TYPE02.value),
            region_price=region_price,
        )
        empty_order.region_total_price = region_price
        empty_order.save(update_fields=['region_total_price'])
        percentage_voucher.discounts.add(
            item_discount_factory(
                value=20,
                shelf_type=ShelfType.TYPE01.value,
            )
        )
        price = percentage_voucher.calculate_promo_amount(
            empty_order,
            region=empty_order.region,
        )
        assert price.quantize(Decimal('.01')) == 100.0

    @patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=1000)
    @patch('gallery.models.Jetty.get_delivery_price', return_value=0)
    def test_calculate_price_for_voucher_with_multiple_discounts(
        self,
        mocked_delivery_price,
        mocked_shelf_price_as_number,
        order_item_factory,
        item_discount_factory,
        jetty_factory,
        percentage_voucher,
        empty_order,
        rates_neutral_region,
    ):
        order_item_1 = order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(
                shelf_type=ShelfType.TYPE02.value,
            ),
            region_price=1000,
        )
        order_item_2 = order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(
                shelf_type=ShelfType.TYPE01.value,
            ),
            region_price=1000,
        )
        order_item_3 = order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(shelf_type=ShelfType.VENEER_TYPE01.value),
            region_price=1000,
        )
        empty_order.region_total_price = (
            order_item_1.region_price
            + order_item_2.region_price
            + order_item_3.region_price
        )
        empty_order.save(update_fields=['region_total_price'])
        percentage_voucher.discounts.add(
            item_discount_factory(
                value=50,
                shelf_type=ShelfType.TYPE02.value,
            )
        )
        percentage_voucher.discounts.add(
            item_discount_factory(
                value=20,
                shelf_type=ShelfType.TYPE01.value,
            )
        )
        price = percentage_voucher.calculate_promo_amount(
            empty_order,
            region=rates_neutral_region,
        )
        assert price.quantize(Decimal('.01')) == 800.0

    @patch('gallery.models.Watty.get_shelf_price_as_number', return_value=1000)
    @patch('gallery.models.Watty.get_delivery_price', return_value=0)
    def test_voucher_with_multiple_types_in_item_conditions(
        self,
        mocked_delivery_price,
        mocked_shelf_price_as_number,
        order_item_factory,
        watty_factory,
        empty_order,
        rates_neutral_region,
        voucher_factory,
    ):
        region_price = 1000
        order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=watty_factory(shelf_type=ShelfType.TYPE13.value),
            region_price=region_price,
        )
        empty_order.region_total_price = region_price
        empty_order.save(update_fields=['region_total_price'])
        voucher = voucher_factory(
            kind_of=VoucherType.PERCENTAGE,
            value=20,
            item_conditionals={
                'shelf_types': [ShelfType.TYPE01.value, ShelfType.TYPE13.value],
            },
        )
        price = voucher.calculate_promo_amount(
            empty_order,
            region=empty_order.region,
        )
        assert price.quantize(Decimal('.01')) == 200.0

    @pytest.mark.parametrize(
        ('start_date', 'end_date'),
        [
            (NOW, NOW - timedelta(days=2)),
            (NOW, NOW - timedelta(hours=2)),
        ],
    )
    def test_voucher_with_end_date_before_start_date_raises_validation_error(
        self, start_date, end_date, voucher_factory
    ):
        with pytest.raises(ValidationError) as excinfo:
            voucher_factory(
                start_date=start_date,
                end_date=end_date,
            )

        assert 'end date cannot be before start date' in str(excinfo.value).lower()


@pytest.mark.django_db
class TestVoucherBundles:
    @patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=1000)
    @patch('gallery.models.Jetty.get_delivery_price', return_value=0)
    def test_calculate_price_for_bundle_apply(
        self,
        mocked_delivery_price,
        mocked_shelf_price_as_number,
        order_item_factory,
        item_discount_factory,
        jetty_factory,
        voucher_factory,
        voucher_bundle_factory,
        empty_order,
        rates_neutral_region,
    ):
        # voucher setup
        voucher = voucher_factory(
            value=10,
            kind_of=VoucherType.PERCENTAGE,
        )
        item_discount1 = item_discount_factory(
            value=50,
            furniture_category=FurnitureCategory.BOOKCASE.value,
        )
        item_discount2 = item_discount_factory(
            value=50,
            furniture_category=FurnitureCategory.DESK.value,
        )
        voucher_bundle_factory(
            voucher=voucher,
            item_discounts=[item_discount1, item_discount2],
        )

        # order setup
        order_item_1 = order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(
                shelf_type=ShelfType.TYPE01.value,
                shelf_category=FurnitureCategory.BOOKCASE.value,
            ),
            region_price=1000,
        )

        order_item_3 = order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(
                shelf_type=ShelfType.TYPE01.value,
                shelf_category=FurnitureCategory.WALL_STORAGE.value,
            ),
            region_price=1000,
        )
        empty_order.region_total_price = (
            order_item_1.region_price + order_item_3.region_price
        )
        empty_order.save(update_fields=['region_total_price'])

        price = voucher.calculate_promo_amount(
            empty_order,
            region=rates_neutral_region,
        )
        # bundle not working
        assert price.quantize(Decimal('.01')) == 200

        order_item_2 = order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(
                shelf_type=ShelfType.TYPE01.value,
                shelf_category=FurnitureCategory.DESK.value,
            ),
            region_price=1000,
        )

        empty_order.region_total_price = (
            order_item_1.region_price
            + order_item_2.region_price
            + order_item_3.region_price
        )
        empty_order.save(update_fields=['region_total_price'])
        price = voucher.calculate_promo_amount(
            empty_order,
            region=rates_neutral_region,
        )
        # bundle working
        assert price.quantize(Decimal('.01')) == 1100

    def test_queries_count(
        self,
        order_item_factory,
        item_discount_factory,
        jetty_factory,
        percentage_voucher,
        voucher_factory,
        voucher_bundle_factory,
        empty_order,
        rates_neutral_region,
        django_assert_max_num_queries,
    ):
        item_discount1 = item_discount_factory(
            value=50,
            furniture_category=FurnitureCategory.BOOKCASE.value,
        )
        item_discount2 = item_discount_factory(
            value=50,
            furniture_category=FurnitureCategory.DESK.value,
        )
        voucher_bundle_factory(
            voucher=percentage_voucher,
            item_discounts=[item_discount1, item_discount2],
        )
        order_item_1 = order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(
                shelf_type=ShelfType.TYPE01.value,
                shelf_category=FurnitureCategory.BOOKCASE.value,
            ),
            region_price=1000,
        )
        order_item_2 = order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(
                shelf_type=ShelfType.TYPE01.value,
                shelf_category=FurnitureCategory.DESK.value,
            ),
            region_price=1000,
        )
        order_item_3 = order_item_factory(
            order=empty_order,
            region=rates_neutral_region,
            order_item=jetty_factory(
                shelf_type=ShelfType.TYPE01.value,
                shelf_category=FurnitureCategory.WALL_STORAGE.value,
            ),
            region_price=1000,
        )
        empty_order.region_total_price = (
            order_item_1.region_price
            + order_item_2.region_price
            + order_item_3.region_price
        )
        empty_order.save(update_fields=['region_total_price'])
        voucher = voucher_factory(
            value=10,
            kind_of=VoucherType.PERCENTAGE,
        )
        voucher.discounts.add(item_discount1, item_discount2)
        with django_assert_max_num_queries(28):  # 28
            percentage_voucher.calculate_promo_amount(
                empty_order,
                region=rates_neutral_region,
            )

        with django_assert_max_num_queries(1):
            voucher.calculate_promo_amount(
                empty_order,
                region=rates_neutral_region,
            )
