from functools import lru_cache
from typing import TYPE_CHECKING

from gallery.types import SellableItemType
if TYPE_CHECKING:
    from vouchers.models import ItemDiscount


@lru_cache(maxsize=1000)
def does_discount_apply(
    discount: 'ItemDiscount',
    item: SellableItemType,
) -> bool:
    """If all filter fields are the same at self and item - then the discount is
    valid. In other words - filters are validated on AND logic.
    If ItemDiscount is about a service - it's not counted for one item, but for
    whole order.
    Made a separate function to make caching easier.
    """
    if discount.service_type:
        return False
    for filter_field in discount.FILTER_FIELDS:
        if getattr(discount, filter_field) in {None, ''}:
            # if instance does not store value in given field - skip
            continue
        try:
            if getattr(item, filter_field) != getattr(discount, filter_field):
                # if discount value is different from item quality -
                # discount is not valid
                return False
        except AttributeError:
            # item might be a jetty, watty or sample box, and they vary in fields
            # we're filtering on. So if we want to discount box_variant this should
            # not apply to jetty, etc.
            return False
    return True
